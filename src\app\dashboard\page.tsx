"use client";

import { <PERSON><PERSON> } from "@/components/ui/8bit/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/8bit/card";
import { Progress } from "@/components/ui/8bit/progress";

export default () => {
  return (
    <div className="pixelated-text min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 crt scanlines relative">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-2 glitch pixelated-text">
            🎮 Dashboard
          </h1>
          <p className="text-white/70 pixelated-text">
            Welcome to your pixelated gaming dashboard!
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card className="bg-black/20 backdrop-blur-sm border-2 border-green-400/30 hover:border-green-400/60 transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white/80 pixelated-text">
                ⚡ Power Level
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-400 mb-2 pixelated-text">
                9001
              </div>
              <Progress value={90} className="h-2" />
            </CardContent>
          </Card>

          <Card className="bg-black/20 backdrop-blur-sm border-2 border-yellow-400/30 hover:border-yellow-400/60 transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white/80 pixelated-text">
                🏆 Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-400 mb-2 pixelated-text">
                42
              </div>
              <div className="text-xs text-white/60 pixelated-text">
                +3 this week
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 backdrop-blur-sm border-2 border-blue-400/30 hover:border-blue-400/60 transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white/80 pixelated-text">
                💎 Gems
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-400 mb-2 pixelated-text">
                1,337
              </div>
              <div className="text-xs text-white/60 pixelated-text">
                Legendary status
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/20 backdrop-blur-sm border-2 border-red-400/30 hover:border-red-400/60 transition-colors">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white/80 pixelated-text">
                🔥 Streak
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-400 mb-2 pixelated-text">
                15
              </div>
              <div className="text-xs text-white/60 pixelated-text">
                days active
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <Card className="lg:col-span-2 bg-black/20 backdrop-blur-sm border-2 border-purple-400/30 hover:border-purple-400/60 transition-colors">
            <CardHeader>
              <CardTitle className="text-white pixelated-text">
                📊 Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-white/5 rounded border border-white/10">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center text-xs pixelated-text">
                    ✓
                  </div>
                  <div>
                    <div className="text-white font-medium pixelated-text">
                      Quest Completed
                    </div>
                    <div className="text-white/60 text-sm pixelated-text">
                      The Pixel Dungeon
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/5 rounded border border-white/10">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center text-xs pixelated-text">
                    🏆
                  </div>
                  <div>
                    <div className="text-white font-medium pixelated-text">
                      Achievement Unlocked
                    </div>
                    <div className="text-white/60 text-sm pixelated-text">
                      Speed Runner
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/5 rounded border border-white/10">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-500 rounded flex items-center justify-center text-xs pixelated-text">
                    💎
                  </div>
                  <div>
                    <div className="text-white font-medium pixelated-text">
                      Rare Item Found
                    </div>
                    <div className="text-white/60 text-sm pixelated-text">
                      Mystic Sword of Coding
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-black/20 backdrop-blur-sm border-2 border-cyan-400/30 hover:border-cyan-400/60 transition-colors">
            <CardHeader>
              <CardTitle className="text-white pixelated-text">
                🚀 Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white pixelated-text">
                🎯 Start New Quest
              </Button>
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white pixelated-text">
                📈 View Stats
              </Button>
              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white pixelated-text">
                🎒 Inventory
              </Button>
              <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white pixelated-text">
                ⚙️ Settings
              </Button>
              <Button
                variant="outline"
                className="w-full bg-transparent border-red-400 text-red-300 hover:bg-red-600/20 pixelated-text"
              >
                🚪 Logout
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
