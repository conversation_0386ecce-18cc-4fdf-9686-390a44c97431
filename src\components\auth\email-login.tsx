"use client";

import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/8bit/button";
import { Input } from "@/components/ui/8bit/input";
import { Label } from "@/components/ui/8bit/label";
import { Alert } from "@/components/ui/8bit/alert";

export default ({ captchaToken }: { captchaToken: string | null }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!captchaToken) {
      setError("Please complete the CAPTCHA");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await authClient.signIn.email({
        email,
        password,
        fetchOptions: {
          headers: {
            "x-captcha-response": captchaToken,
          },
        },
      });

      router.push("/dashboard");
    } catch (err: any) {
      setError(err.message || "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full">
      <h2 className="text-lg font-semibold text-white text-center mb-2 pixelated-text">
        📧 Sign In with Email
      </h2>

      {error && (
        <Alert className="bg-red-900/50 border-red-500 text-red-200 pixelated-text">
          ❌ {error}
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="email" className="text-white/90 pixelated-text">
          📬 Email
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Enter your email..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-white/90 pixelated-text">
          🔒 Password
        </Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Enter your password..."
        />
      </div>

      <Button
        type="submit"
        disabled={isLoading || !captchaToken}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-600 disabled:cursor-not-allowed pixelated-text"
      >
        {isLoading ? "🔄 Signing In..." : "🚀 Sign In"}
      </Button>
    </form>
  );
};
