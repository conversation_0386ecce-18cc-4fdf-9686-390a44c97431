"use client";

import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/8bit/button";
import { Input } from "@/components/ui/8bit/input";
import { Label } from "@/components/ui/8bit/label";
import { Alert } from "@/components/ui/8bit/alert";

export default ({ captchaToken }: { captchaToken: string | null }) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (!captchaToken) {
      setError("Please complete the CAPTCHA");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await authClient.signUp.email({
        name,
        email,
        password,
        fetchOptions: {
          headers: {
            "x-captcha-response": captchaToken,
          },
        },
        callbackURL: "/dashboard",
      });
      router.push("/dashboard");
    } catch (err: any) {
      setError(err.message || "Signup failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full">
      <h2 className="text-lg font-semibold text-white text-center mb-2 pixelated-text">
        ✨ Sign Up with Email
      </h2>

      {error && (
        <Alert className="bg-red-900/50 border-red-500 text-red-200 pixelated-text">
          ❌ {error}
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="name" className="text-white/90 pixelated-text">
          👤 Name
        </Label>
        <Input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Enter your name..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-white/90 pixelated-text">
          📬 Email
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Enter your email..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-white/90 pixelated-text">
          🔒 Password
        </Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Enter your password..."
        />
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="confirmPassword"
          className="text-white/90 pixelated-text"
        >
          🔐 Confirm Password
        </Label>
        <Input
          id="confirmPassword"
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          className="bg-black/30 text-white border-white/30 placeholder:text-white/50 pixelated-text"
          placeholder="Confirm your password..."
        />
      </div>

      <Button
        type="submit"
        disabled={isLoading || !captchaToken}
        className="w-full bg-green-600 hover:bg-green-700 text-white disabled:bg-gray-600 disabled:cursor-not-allowed pixelated-text"
      >
        {isLoading ? "🔄 Signing Up..." : "🎉 Sign Up"}
      </Button>
    </form>
  );
};
