import { auth } from "@/lib/auth";
import { toNextJsHandler } from "better-auth/next-js";

const handler = toNextJsHandler(auth);

export const GET = async (request: Request) => {
	try {
		return await handler.GET(request);
	} catch (error) {
		console.error("Auth GET error:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
};

export const POST = async (request: Request) => {
	try {
		return await handler.POST(request);
	} catch (error) {
		console.error("Auth POST error:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
};
