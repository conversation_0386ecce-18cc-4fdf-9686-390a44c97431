services:
  postgres:
    image: postgres:latest
    env_file: .env
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    env_file: .env
    restart: unless-stopped
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio:latest
    env_file: .env
    restart: unless-stopped
    command: server /data --console-address ":${S3_UI_PORT}"
    environment:
      MINIO_ROOT_USER: ${S3_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${S3_SECRET_KEY}
    ports:
      - "${S3_PORT}:9000"
      - "${S3_UI_PORT}:9001"
    volumes:
      - minio_data:/data


volumes:
  postgres_data:
  redis_data:
  minio_data:
